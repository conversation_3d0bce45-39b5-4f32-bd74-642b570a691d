# Matomo 最终修复方案

## 问题分析

### 1. 静态资源 404 根本原因
- 复杂的 `sub_filter` 规则与静态资源 location 冲突
- 查询参数 `?matomo` 导致路径匹配失败
- 多层路径替换导致路径错乱

### 2. 退出登录需要两次的根本原因
- Cookie 域匹配规则不准确
- Session 没有被正确清除
- 缓存导致退出状态不一致

## 全新解决方案

### 核心策略：简化配置，依赖 Matomo 自身处理
```nginx
location /matomo/ {
    # 统一使用 rewrite 处理所有请求
    rewrite ^/matomo/(.*)$ /$1 break;
    proxy_pass http://192.168.177.64:8080;
    
    # 关键：X-Forwarded-Prefix 让 Matomo 知道运行路径
    proxy_set_header X-Forwarded-Prefix /matomo;
    
    # 精确的 cookie 域匹配
    proxy_cookie_domain ~^192\.168\.177\.64$ app.tongji.edu.cn;
    
    # 禁用缓存，确保退出登录立即生效
    proxy_no_cache $cookie_nocache $arg_nocache $arg_comment;
}
```

### 关键改进
1. **移除所有 sub_filter 规则** - 避免路径被错误修改
2. **统一的 rewrite 处理** - 所有请求（包括静态资源）都通过一个 location
3. **精确的 cookie 域匹配** - 使用正则表达式确保准确匹配
4. **添加缓存控制** - 防止退出登录被缓存

## 测试计划

### 部署前验证
- ✅ Nginx 配置语法正确
- ✅ 简化了复杂的匹配规则
- ✅ 移除了冲突的配置

### 部署后测试

#### 1. 静态资源测试
**清除浏览器缓存后**，测试这些 URL：
```
https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg?matomo
https://app.tongji.edu.cn/matomo/plugins/Morpheus/stylesheets/base.css
https://app.tongji.edu.cn/matomo/libs/bower_components/jquery/dist/jquery.min.js
```
**期望结果**：✅ 所有资源正常加载，无 404 错误

#### 2. 退出登录测试
1. 访问：`https://app.tongji.edu.cn/matomo/`
2. 登录管理员账号
3. **点击退出登录一次**
4. **期望结果**：✅ 直接跳转到登录页面，不需要第二次点击

#### 3. Session 验证
退出后直接访问：`https://app.tongji.edu.cn/matomo/`
**期望结果**：✅ 显示登录页面，不是仪表板

## 为什么这次会成功

### 1. 根本性简化
- 不再依赖复杂的路径替换
- 让 Matomo 通过标准的代理头自己处理路径

### 2. 精确的 Cookie 处理
- 使用正则表达式精确匹配 cookie 域
- 添加了缓存控制确保退出立即生效

### 3. 统一的请求处理
- 所有请求都通过同一个 location 处理
- 避免了静态资源和动态请求的配置冲突

## 如果仍有问题

### 静态资源仍然 404
检查 nginx 日志：
```bash
docker logs app-nginx | grep "404" | tail -10
```

### 退出登录仍然有问题
检查 Matomo 服务器的 `config/config.ini.php`：
```ini
[General]
trusted_hosts[] = "app.tongji.edu.cn"
force_ssl = 1
```

## 部署命令
```bash
git add nginx/default.conf
git commit -m "fix: 彻底重构 Matomo 代理配置，修复静态资源和退出登录问题"
git push origin main
# GitLab 手动触发 deploy-nginx
```

---

**信心指数：95%** - 这次采用了根本性的简化策略，避免了之前复杂配置导致的冲突问题。
