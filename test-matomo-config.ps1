# Matomo 配置测试脚本
# 在部署前本地验证配置

Write-Host "=== Matomo Nginx 配置测试 ===" -ForegroundColor Green

# 1. 检查 nginx 配置语法
Write-Host "`n1. 检查 nginx 配置语法..." -ForegroundColor Yellow
$nginxTest = docker run --rm -v "${PWD}/nginx:/etc/nginx/conf.d" nginx:1.26.3 nginx -t 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Nginx 配置语法正确" -ForegroundColor Green
} else {
    Write-Host "❌ Nginx 配置语法错误:" -ForegroundColor Red
    Write-Host $nginxTest -ForegroundColor Red
    exit 1
}

# 2. 检查 Matomo 后端连通性
Write-Host "`n2. 检查 Matomo 后端连通性..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://**************:8080/" -Method Head -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ Matomo 后端可访问 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ Matomo 后端不可访问: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请确认 Matomo 服务运行在 **************:8080" -ForegroundColor Yellow
}

# 3. 分析配置变更
Write-Host "`n3. 配置变更分析..." -ForegroundColor Yellow
Write-Host "✅ 移除了复杂的 sub_filter 规则" -ForegroundColor Green
Write-Host "✅ 使用 rewrite 统一处理所有请求" -ForegroundColor Green
Write-Host "✅ 修复了 cookie 域匹配规则" -ForegroundColor Green
Write-Host "✅ 添加了缓存控制防止退出登录缓存" -ForegroundColor Green

# 4. 预期修复的问题
Write-Host "`n4. 预期修复的问题..." -ForegroundColor Yellow
Write-Host "🔧 静态资源 404: https://app.tongji.edu.cn/plugins/Morpheus/images/logo.svg?matomo" -ForegroundColor Cyan
Write-Host "🔧 退出登录需要两次点击的问题" -ForegroundColor Cyan

# 5. 测试建议
Write-Host "`n5. 部署后测试步骤..." -ForegroundColor Yellow
Write-Host "1. 清除浏览器缓存和 cookies" -ForegroundColor White
Write-Host "2. 访问: https://app.tongji.edu.cn/matomo/" -ForegroundColor White
Write-Host "3. 检查静态资源是否正常加载（开发者工具 Network 面板）" -ForegroundColor White
Write-Host "4. 登录后测试退出功能（应该一次退出成功）" -ForegroundColor White

# 6. 关键测试 URL
Write-Host "`n6. 关键测试 URL..." -ForegroundColor Yellow
$testUrls = @(
    "https://app.tongji.edu.cn/matomo/",
    "https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg?matomo",
    "https://app.tongji.edu.cn/matomo/plugins/Morpheus/stylesheets/base.css",
    "https://app.tongji.edu.cn/matomo/libs/bower_components/jquery/dist/jquery.min.js"
)

foreach ($url in $testUrls) {
    Write-Host "📋 $url" -ForegroundColor White
}

# 7. 如果问题仍然存在
Write-Host "`n7. 如果问题仍然存在..." -ForegroundColor Yellow
Write-Host "请收集以下信息:" -ForegroundColor White
Write-Host "- 浏览器开发者工具 Network 面板截图" -ForegroundColor White
Write-Host "- nginx 日志: docker logs app-nginx | tail -50" -ForegroundColor White
Write-Host "- 具体的错误 URL 和状态码" -ForegroundColor White

Write-Host "`n=== 配置测试完成 ===" -ForegroundColor Green
Write-Host "如果语法检查通过，可以部署配置进行实际测试" -ForegroundColor Green
