variables:
  IMAGE_TAG: "${CI_PIPELINE_ID}-${CI_COMMIT_REF_NAME}"  # 流水线ID-分支名
stages:
  - build
  - deploy

.build-sha:
  image: docker:stable
  stage: build
  before_script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:

    # 镜像构建

    - echo $IMAGE_TAG
    - docker build -f $DOCKERFILE_PATH/Dockerfile $RELITIVE_PATH/ -t $CI_REGISTRY_IMAGE:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE:$IMAGE_TAG

  tags:
    - unicom-docker #dockerper

build-nginx-sha:
  image: docker:stable
  stage: build
  extends: .build-sha
  variables:
    CONTAINER_NAME: "nginx"
    DOCKERFILE_PATH: "nginx"
    RELITIVE_PATH: "."
  before_script:
    # 准备 SSL 文件
    - cd ./nginx/
    - cp $SSL_KEY ./ssl_key.key
    - cp $SSL_CRT ./ssl_crt.crt
    - cd ../
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  only:
    - main


deploy-nginx:
  stage: deploy
  image: ringcentral/sshpass
  script:
    - echo $CI_REGISTRY_IMAGE
    - sshpass -p $SSH_PASSWORD_PRD ssh -p $PORT -o StrictHostKeyChecking=no $USER@$IPADDRESS "docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY;
      docker pull $CI_REGISTRY_IMAGE:$IMAGE_TAG;
      docker stop app-nginx || true;
      docker rm app-nginx || true;
      docker run -d --name app-nginx -p 80:80 --restart always --network host $CI_REGISTRY_IMAGE:$IMAGE_TAG;
      docker image prune -af --filter \"until=24h\""
  only:
    - main
  when: manual  # 手动触发
