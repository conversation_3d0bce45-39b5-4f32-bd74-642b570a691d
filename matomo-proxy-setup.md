# Matomo 代理配置修复指南

## 问题描述
登录 Matomo 后跳转到 `https://app.tongji.edu.cn/index.php` 导致 404 错误。

## 解决方案

### 1. Nginx 代理配置 (已修复)

已更新 `nginx/default.conf` 中的 `/matomo/` location 配置：

- ✅ 添加了正确的 `X-Forwarded-Prefix` 头
- ✅ 修复了 `proxy_redirect` 规则处理 index.php 重定向
- ✅ 添加了全面的 `sub_filter` 规则重写页面中的路径
- ✅ 修复了 cookie 域和路径设置
- ✅ 添加了适当的 CORS 头

### 2. Matomo 服务器端配置 (需要检查)

在 Matomo 服务器 (**************:8080) 上，需要检查以下配置：

#### 2.1 config/config.ini.php 配置

```ini
[General]
; 添加信任的主机
trusted_hosts[] = "app.tongji.edu.cn"
trusted_hosts[] = "**************:8080"

; 如果使用代理，启用代理支持
proxy_client_headers[] = "HTTP_X_FORWARDED_FOR"
proxy_client_headers[] = "HTTP_X_REAL_IP"
proxy_host_headers[] = "HTTP_X_FORWARDED_HOST"

; 强制使用 HTTPS
force_ssl = 1

; 设置正确的根 URL (如果需要)
; assume_secure_protocol = 1
```

#### 2.2 .htaccess 配置 (如果使用 Apache)

确保 Matomo 根目录的 `.htaccess` 文件包含：

```apache
# 处理代理头
SetEnvIf X-Forwarded-Proto "https" HTTPS=on
SetEnvIf X-Forwarded-Prefix "^(.+)$" SCRIPT_NAME=$1
```

### 3. 测试步骤

1. **部署更新的 nginx 配置**：
   ```bash
   # 测试配置
   ./test-matomo-proxy.sh
   
   # 重新部署 nginx
   docker-compose down
   docker-compose up -d
   ```

2. **访问测试**：
   - 访问：`https://app.tongji.edu.cn/matomo/`
   - 登录并检查重定向是否正确

3. **检查日志**：
   ```bash
   # Nginx 日志
   docker logs nginx
   
   # Matomo 日志 (在 Matomo 服务器上)
   tail -f /path/to/matomo/tmp/logs/piwik.log
   ```

### 4. 常见问题排查

#### 4.1 仍然跳转到错误地址
- 检查 Matomo 的 `config.ini.php` 中的 `trusted_hosts` 设置
- 确认 Matomo 服务器能正确处理 `X-Forwarded-Prefix` 头

#### 4.2 静态资源加载失败
- 检查浏览器开发者工具的网络面板
- 确认所有资源路径都正确添加了 `/matomo/` 前缀

#### 4.3 登录后会话丢失
- 检查 cookie 设置
- 确认 `proxy_cookie_domain` 和 `proxy_cookie_path` 配置正确

### 5. 高级配置选项

如果问题仍然存在，可以尝试以下高级配置：

#### 5.1 在 Matomo 中设置环境变量
```bash
# 在 Matomo 容器或服务器上设置
export HTTP_X_FORWARDED_PREFIX="/matomo"
export HTTP_X_FORWARDED_HOST="app.tongji.edu.cn"
export HTTP_X_FORWARDED_PROTO="https"
```

#### 5.2 使用 Matomo 的代理配置
在 Matomo 的 `config/config.ini.php` 中：

```ini
[General]
; 明确设置代理 URI 前缀
proxy_uri_header = 1
```

### 6. 验证清单

- [ ] Nginx 配置已更新并重新加载
- [ ] Matomo `trusted_hosts` 包含 `app.tongji.edu.cn`
- [ ] 可以访问 `https://app.tongji.edu.cn/matomo/`
- [ ] 登录后不再跳转到错误地址
- [ ] 所有静态资源正确加载
- [ ] 功能正常工作（创建网站、查看报告等）

## 联系支持

如果问题仍然存在，请提供：
1. Nginx 错误日志
2. Matomo 错误日志
3. 浏览器开发者工具的网络请求详情
4. Matomo 的 `config.ini.php` 配置（隐藏敏感信息）
