# Matomo 代理修复部署指南

## 问题总结
登录 Matomo 后跳转到 `https://app.tongji.edu.cn/index.php` 导致 404 错误。

## 已应用的修复

### 1. Nginx 配置修复 ✅

已更新 `nginx/default.conf` 中的 Matomo 代理配置，主要修复：

- **添加关键代理头**：
  - `X-Forwarded-Prefix: /matomo` - 告诉 Matomo 它运行在子路径下
  - `X-Forwarded-Host: app.tongji.edu.cn` - 正确的主机名
  - `X-Forwarded-Uri: /matomo` - URI 前缀

- **修复重定向规则**：
  - 特别处理 `index.php` 重定向
  - 处理所有可能的后端重定向

- **内容路径重写**：
  - 使用 `sub_filter` 重写 HTML/JS/CSS 中的绝对路径
  - 修复 Matomo 特定的 JavaScript 变量

- **Cookie 修复**：
  - 正确设置 cookie 域和路径

## 部署步骤

### 1. 立即部署 (推荐)
```bash
# 如果使用 GitLab CI/CD
git add .
git commit -m "fix: 修复 Matomo 代理重定向问题"
git push origin main

# 然后在 GitLab 中手动触发 deploy-nginx 任务
```

### 2. 手动部署 (如果需要)
```bash
# 在服务器上
docker pull your-registry/nginx:latest
docker stop app-nginx
docker rm app-nginx
docker run -d --name app-nginx -p 80:80 --restart always --network host your-registry/nginx:latest
```

## 验证步骤

### 1. 基本访问测试
1. 访问：`https://app.tongji.edu.cn/matomo/`
2. 确认页面正常加载，没有 404 错误

### 2. 登录测试
1. 使用 Matomo 管理员账号登录
2. **关键测试**：登录后应该跳转到 `https://app.tongji.edu.cn/matomo/index.php?...` 而不是 `https://app.tongji.edu.cn/index.php`

### 3. 功能测试
1. 检查仪表板是否正常显示
2. 尝试访问不同的 Matomo 功能页面
3. 确认所有静态资源（CSS、JS、图片）正确加载

## 如果问题仍然存在

### Matomo 服务器端配置检查

需要在 Matomo 服务器 (**************:8080) 上检查：

1. **config/config.ini.php**：
```ini
[General]
trusted_hosts[] = "app.tongji.edu.cn"
trusted_hosts[] = "**************:8080"
force_ssl = 1
```

2. **检查 Matomo 日志**：
```bash
# 在 Matomo 服务器上
tail -f /path/to/matomo/tmp/logs/piwik.log
```

### 调试信息收集

如果问题持续，请收集：

1. **浏览器开发者工具**：
   - Network 面板中的请求/响应详情
   - Console 中的 JavaScript 错误

2. **Nginx 日志**：
```bash
docker logs app-nginx
```

3. **具体的重定向 URL**：
   - 登录前的 URL
   - 登录后实际跳转的 URL
   - 期望的 URL

## 预期结果

修复后的行为：
- ✅ 访问 `https://app.tongji.edu.cn/matomo/` 正常
- ✅ 登录后跳转到 `https://app.tongji.edu.cn/matomo/index.php?module=CoreHome&action=index`
- ✅ 所有 Matomo 功能正常工作
- ✅ 静态资源正确加载

## 紧急回滚

如果新配置导致问题，可以快速回滚：

```bash
# 恢复到之前的配置
git revert HEAD
git push origin main
# 重新部署
```

---

**注意**：这个修复主要解决了 nginx 代理层面的问题。如果 Matomo 服务器本身的配置有问题，可能还需要额外的服务器端配置调整。
