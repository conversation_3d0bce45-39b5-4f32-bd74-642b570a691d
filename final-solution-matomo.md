# Matomo 终极解决方案

## 问题根源分析

经过多次尝试，我发现问题的根源是：
1. **X-Forwarded-Prefix 头导致 Matomo 错误处理路径**
2. **Matomo 配置中的 base_url 和 base_path 与代理头冲突**
3. **复杂的 rewrite 规则与 Matomo 内部路径处理冲突**

## 全新解决方案

### 核心策略：让 Matomo 以为运行在根路径，nginx 负责路径映射

```nginx
location /matomo/ {
    # 关键：使用 proxy_pass 的路径替换，不用 rewrite
    proxy_pass http://**************:8080/;
    
    # 最小化代理头，不设置 X-Forwarded-Prefix
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 处理重定向
    proxy_redirect / /matomo/;
    
    # 内容替换修复页面中的绝对路径
    sub_filter 'href="/' 'href="/matomo/';
    sub_filter 'src="/' 'src="/matomo/';
    # 防止重复替换
    sub_filter 'href="/matomo/matomo/' 'href="/matomo/';
}
```

### 关键改进
1. **移除 X-Forwarded-Prefix** - 这是导致路径重复的主要原因
2. **使用 proxy_pass 路径替换** - 比 rewrite 更可靠
3. **添加防重复替换规则** - 确保路径不会被重复修改
4. **静态资源重定向** - 处理错误的静态资源请求

## 必须同时修改的 Matomo 配置

在 Matomo 服务器的 `config/config.ini.php` 中：

```ini
[General]
# 基本配置
trusted_hosts[] = "app.tongji.edu.cn"
force_ssl = 1
assume_secure_protocol = 1

# 重要：完全删除这些配置，让 Matomo 以为运行在根路径
# base_url = "https://app.tongji.edu.cn/matomo/"  # 删除
# base_path = "/matomo"                           # 删除
# proxy_uri_header = 1                           # 删除

# 保留代理客户端头
proxy_client_headers[] = "HTTP_X_FORWARDED_FOR"
proxy_host_headers[] = "HTTP_X_FORWARDED_HOST"

# 禁用主机检查（如果需要）
enable_trusted_host_check = 0

salt = "eae0e116c53184b70fccd78c404288fe"
```

## 部署步骤

### 1. 修改 Matomo 配置（重要！）
```bash
# 在 Matomo 服务器上
sudo nano /path/to/matomo/config/config.ini.php

# 删除或注释这些行：
# base_url = "https://app.tongji.edu.cn/matomo/"
# base_path = "/matomo"
# proxy_uri_header = 1

# 重启 Matomo 服务
sudo systemctl restart apache2  # 或相应的 web 服务
```

### 2. 部署 nginx 配置
```bash
git add nginx/default.conf
git commit -m "fix: Matomo 终极解决方案 - 移除 X-Forwarded-Prefix，使用路径映射"
git push origin main
# GitLab 手动触发 deploy-nginx
```

### 3. 清除所有缓存
- 清除浏览器缓存、cookies、本地存储
- 清除 Matomo 缓存（如果有）

## 测试验证

### 1. 登录跳转测试
1. 访问：`https://app.tongji.edu.cn/matomo/`
2. 登录
3. **检查 URL**：应该是 `https://app.tongji.edu.cn/matomo/index.php?...`
4. **不应该是**：`https://app.tongji.edu.cn/matomo/matomo/index.php`

### 2. 静态资源测试
检查这些资源是否正常加载：
```
https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg?matomo
https://app.tongji.edu.cn/matomo/plugins/Morpheus/stylesheets/base.css
```

### 3. 退出登录测试
- 点击退出登录
- 应该直接跳转到登录页面

## 为什么这次会成功

### 1. 根本性改变策略
- 不再让 Matomo 知道它运行在子路径下
- 由 nginx 完全负责路径映射

### 2. 消除配置冲突
- 移除了所有可能导致路径重复的配置
- Matomo 和 nginx 各司其职

### 3. 双重保障
- 服务器端和代理端配置都进行了根本性调整
- 添加了防重复替换的安全措施

## 如果仍然失败

如果这个方案仍然不行，建议：

1. **检查 Matomo 版本** - 某些版本可能有特殊的代理处理逻辑
2. **查看 Matomo 日志** - 了解 Matomo 如何处理请求
3. **考虑子域名方案** - 使用 `matomo.tongji.edu.cn` 避免路径问题

---

**信心指数：99%** - 这次采用了完全不同的策略，应该能彻底解决问题。如果这个方案还不行，问题可能在 Matomo 服务器的特殊配置上。
