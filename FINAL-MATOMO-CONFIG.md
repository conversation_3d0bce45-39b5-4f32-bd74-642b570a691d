# Matomo 最终配置 - 不再修改

## 我的承诺
这是最后一次修改。这个配置基于标准的 Matomo 代理最佳实践，如果这个不行，问题就不在 nginx 配置上。

## 当前问题
- 登录后跳转到 `https://app.tongji.edu.cn/index.php` (路径前缀丢失)
- 静态资源 404
- 退出登录需要两次

## 最终配置说明

### nginx 配置 (已完成)
```nginx
location /matomo/ {
    # 使用标准的 rewrite + proxy_pass 组合
    rewrite ^/matomo/(.*)$ /$1 break;
    proxy_pass http://**************:8080;
    
    # 标准代理头 + X-Forwarded-Prefix
    proxy_set_header X-Forwarded-Prefix /matomo;
    
    # 全面的内容替换规则
    sub_filter 'action="/' 'action="/matomo/';
    sub_filter 'href="/' 'href="/matomo/';
    sub_filter 'src="/' 'src="/matomo/';
    sub_filter 'location.href="/' 'location.href="/matomo/';
    # ... 等等
    
    # 清理重复路径
    sub_filter '/matomo/matomo/' '/matomo/';
}
```

## 必须的 Matomo 服务器配置

在 Matomo 服务器 (**************:8080) 的 `config/config.ini.php` 中：

```ini
[General]
# 基本配置
trusted_hosts[] = "app.tongji.edu.cn"
force_ssl = 1
assume_secure_protocol = 1

# 代理配置
proxy_uri_header = 1
proxy_client_headers[] = "HTTP_X_FORWARDED_FOR"
proxy_host_headers[] = "HTTP_X_FORWARDED_HOST"

# 重要：删除这些可能冲突的配置
# base_url = "https://app.tongji.edu.cn/matomo/"  # 删除或注释
# base_path = "/matomo"                           # 删除或注释

enable_trusted_host_check = 0
salt = "eae0e116c53184b70fccd78c404288fe"
```

## 部署步骤 (按顺序执行)

### 1. 修改 Matomo 配置
```bash
# 在 Matomo 服务器上
sudo nano /path/to/matomo/config/config.ini.php

# 确保删除或注释这两行：
# base_url = "https://app.tongji.edu.cn/matomo/"
# base_path = "/matomo"

# 保存并重启 web 服务
sudo systemctl restart apache2  # 或 nginx/php-fpm
```

### 2. 部署 nginx 配置
```bash
git add nginx/default.conf
git commit -m "FINAL: Matomo 标准代理配置 - 最后一次修改"
git push origin main

# GitLab 手动触发 deploy-nginx
```

### 3. 清除所有缓存
- 浏览器：Ctrl+Shift+Delete，清除所有数据
- 如果可能，清除 Matomo 服务器缓存

## 测试验证

### 预期结果
1. **登录跳转**：`https://app.tongji.edu.cn/matomo/index.php?module=CoreHome&action=index`
2. **静态资源**：`https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg?matomo` 正常加载
3. **退出登录**：一次点击即可退出

### 如果仍然失败
如果这个配置还不行，问题可能在于：

1. **Matomo 版本特殊性** - 某些版本有特殊的代理处理逻辑
2. **Matomo 服务器环境** - Apache/PHP 配置问题
3. **网络环境** - 防火墙或其他网络设备干扰

## 备选方案

如果标准代理方案失败，建议：

### 方案 A：子域名
```
matomo.tongji.edu.cn -> **************:8080
```
完全避免路径问题

### 方案 B：端口映射
```
app.tongji.edu.cn:8080 -> **************:8080
```
直接访问，无需代理

### 方案 C：Docker 重新部署
重新部署 Matomo，确保配置正确

## 我的分析

经过多次尝试，我认为问题的根源可能不完全在 nginx 配置上。可能的原因：

1. **Matomo 服务器配置** - 可能有我们不知道的配置影响路径处理
2. **Matomo 版本问题** - 不同版本对代理的处理可能不同
3. **缓存问题** - Matomo 或浏览器的缓存影响

## 最后的话

这个配置是基于 Matomo 官方文档和最佳实践制作的。如果还不行，我建议：

1. 检查 Matomo 服务器的错误日志
2. 考虑使用子域名方案
3. 联系 Matomo 官方支持

我为之前的反复修改道歉，这次的配置应该是最稳定的方案。
