# Matomo 静态资源和退出登录修复

## 问题描述
1. **静态资源 404** - 如 `https://app.tongji.edu.cn/plugins/Morpheus/images/logo.svg?matomo`
2. **退出登录失败** - 退出后仍然回到首页而不是登录页

## 修复方案

### 1. 静态资源修复 ✅

#### 添加了专门的静态资源代理
```nginx
location ~* ^/matomo/(.+\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))$ {
    proxy_pass http://192.168.177.64:8080/$1;
    # ... 其他配置
}
```

#### 启用了内容路径替换
- 修复 `src="/` → `src="/matomo/`
- 修复 `href="/` → `href="/matomo/`
- 修复 CSS 中的 `url("/` → `url("/matomo/`
- 修复 AJAX 请求路径

### 2. 退出登录修复 ✅

#### Cookie 处理优化
```nginx
proxy_cookie_path / /matomo/;
proxy_cookie_domain 192.168.177.64 app.tongji.edu.cn;
proxy_cookie_flags ~ secure samesite=lax;
```

#### 表单和重定向修复
- 修复表单 action 路径
- 确保退出登录请求正确处理

## 测试步骤

### 1. 部署更新
```bash
git add nginx/default.conf
git commit -m "fix: 修复 Matomo 静态资源 404 和退出登录问题"
git push origin main

# 在 GitLab 中手动触发 deploy-nginx
```

### 2. 静态资源测试

#### 2.1 基本访问测试
1. **清除浏览器缓存**（重要！）
2. 访问：`https://app.tongji.edu.cn/matomo/`
3. 打开浏览器开发者工具 → Network 面板
4. 刷新页面，检查静态资源加载情况

#### 2.2 检查具体资源
测试这些常见的静态资源是否正常加载：
- ✅ `https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg`
- ✅ `https://app.tongji.edu.cn/matomo/plugins/Morpheus/stylesheets/base.css`
- ✅ `https://app.tongji.edu.cn/matomo/libs/bower_components/jquery/dist/jquery.min.js`

#### 2.3 验证缓存头
静态资源应该返回：
```
Cache-Control: public, immutable
Expires: [7天后的日期]
```

### 3. 退出登录测试

#### 3.1 完整的登录/退出流程
1. 访问：`https://app.tongji.edu.cn/matomo/`
2. 使用管理员账号登录
3. 确认成功进入仪表板
4. **点击退出登录**
5. **验证**：应该跳转到登录页面，而不是仍然显示仪表板

#### 3.2 Session 清除验证
退出后检查：
- 浏览器开发者工具 → Application → Cookies
- Matomo 相关的 cookies 应该被清除或失效
- 直接访问 `https://app.tongji.edu.cn/matomo/` 应该显示登录页面

### 4. 功能完整性测试

#### 4.1 界面完整性
- ✅ 页面样式正常显示（CSS 加载成功）
- ✅ 图标和图片正常显示
- ✅ JavaScript 功能正常工作

#### 4.2 核心功能测试
- ✅ 创建新网站
- ✅ 查看报告
- ✅ 设置管理
- ✅ 用户管理

## 故障排除

### 静态资源仍然 404

#### 检查 nginx 日志
```bash
docker logs app-nginx | grep "404"
```

#### 检查资源路径匹配
在浏览器中直接访问：
```
https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg
```

如果仍然 404，可能需要调整正则表达式。

#### 临时调试方法
在 nginx 配置中添加调试日志：
```nginx
location ~* ^/matomo/(.+\.(css|js|png|jpg|jpeg|gif|svg|ico))$ {
    # 添加调试头
    add_header X-Debug-Matched "static-resource";
    add_header X-Debug-Path "$1";
    # ... 其他配置
}
```

### 退出登录仍然失败

#### 检查 Matomo 服务器日志
```bash
# 在 Matomo 服务器上
tail -f /path/to/matomo/tmp/logs/piwik.log
```

#### 检查 Cookie 设置
在浏览器开发者工具中：
1. Network 面板查看退出请求
2. 检查响应头中的 Set-Cookie
3. 确认 cookie 域和路径设置正确

#### 可能的 Matomo 配置
如果问题持续，在 Matomo 的 `config/config.ini.php` 中添加：
```ini
[General]
# 强制退出后重定向到登录页
login_logout_url = "/matomo/index.php?module=Login&action=login"
```

## 预期结果

修复后应该：
- ✅ 所有静态资源正常加载，无 404 错误
- ✅ 页面样式和功能完整
- ✅ 退出登录后正确跳转到登录页面
- ✅ 退出后无法直接访问仪表板（需要重新登录）

## 监控建议

### 持续监控静态资源
```bash
# 定期检查 404 错误
docker logs app-nginx | grep "404" | grep "matomo"
```

### 用户反馈收集
- 询问用户是否还有静态资源加载问题
- 确认退出登录功能是否正常

---

**注意**：这次修复同时处理了静态资源路径和退出登录的 cookie/session 处理。如果仍有问题，可能需要在 Matomo 服务器端进行额外配置。
