#!/bin/bash

echo "Testing Matomo proxy configuration..."
echo "=================================="

# 测试 nginx 配置语法
echo "1. Testing nginx configuration syntax..."
docker run --rm -v $(pwd)/nginx:/etc/nginx/conf.d nginx:1.26.3 nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx configuration syntax is valid"
else
    echo "❌ Nginx configuration syntax error"
    exit 1
fi

echo ""
echo "2. Testing Matomo backend connectivity..."
# 测试后端 Matomo 服务是否可访问
curl -s -o /dev/null -w "%{http_code}" http://192.168.177.64:8080/ | grep -q "200\|301\|302"

if [ $? -eq 0 ]; then
    echo "✅ Matomo backend is accessible"
else
    echo "❌ Matomo backend is not accessible"
    echo "Please check if Matomo service is running on 192.168.177.64:8080"
fi

echo ""
echo "3. Configuration summary:"
echo "- Proxy path: /matomo/"
echo "- Backend: http://192.168.177.64:8080/"
echo "- Domain: app.tongji.edu.cn"
echo "- SSL: Enabled"

echo ""
echo "4. Key fixes applied:"
echo "- Added X-Forwarded-Prefix header for proper path handling"
echo "- Fixed proxy_redirect rules for index.php redirects"
echo "- Added comprehensive sub_filter rules for path rewriting"
echo "- Fixed cookie domain and path settings"
echo "- Added proper CORS headers"

echo ""
echo "5. Next steps:"
echo "- Deploy the updated nginx configuration"
echo "- Test login at https://app.tongji.edu.cn/matomo/"
echo "- Check Matomo logs if issues persist"
echo "- Verify Matomo's config.ini.php has correct trusted_hosts setting"
