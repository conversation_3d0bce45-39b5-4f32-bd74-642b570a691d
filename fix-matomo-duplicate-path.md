# 修复 Matomo 重复路径问题

## 问题描述
登录后路径变成了 `https://app.tongji.edu.cn/matomo/matomo/index.php`，出现重复的 `/matomo/` 路径。

## 根本原因
之前的 `sub_filter` 规则过于激进，对已经正确的路径又添加了一次 `/matomo/` 前缀。

## 修复方案

### 1. 简化配置 ✅
- 移除了过度的 `sub_filter` 规则
- 简化了 `proxy_redirect` 规则
- 保留了关键的 `X-Forwarded-Prefix` 头，让 Matomo 自己处理路径

### 2. 当前配置重点
```nginx
location /matomo/ {
    proxy_pass http://**************:8080/;
    
    # 关键代理头
    proxy_set_header Host app.tongji.edu.cn;
    proxy_set_header X-Forwarded-Prefix /matomo;  # 最重要的头
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host app.tongji.edu.cn;
    
    # 简化的重定向规则
    proxy_redirect ~^https?://192\.168\.177\.64:8080/(.*)$ /matomo/$1;
    proxy_redirect ~^https?://app\.tongji\.edu\.cn/(.*)$ /matomo/$1;
    proxy_redirect / /matomo/;
    
    # Cookie 修复
    proxy_cookie_path / /matomo/;
    proxy_cookie_domain ************** app.tongji.edu.cn;
    
    # 暂时禁用内容替换，让 Matomo 自己处理
}
```

## 立即测试步骤

### 1. 部署更新
```bash
# 提交更改
git add nginx/default.conf
git commit -m "fix: 修复 Matomo 重复路径问题，简化代理配置"
git push origin main

# 在 GitLab 中手动触发 deploy-nginx
```

### 2. 测试访问
1. **清除浏览器缓存** (重要！)
2. 访问：`https://app.tongji.edu.cn/matomo/`
3. 登录 Matomo
4. **检查登录后的 URL**：
   - ✅ 期望：`https://app.tongji.edu.cn/matomo/index.php?...`
   - ❌ 错误：`https://app.tongji.edu.cn/matomo/matomo/index.php`

### 3. 如果仍有问题

#### 方案 A：检查 Matomo 服务器配置
在 Matomo 服务器 (**************:8080) 上检查 `config/config.ini.php`：

```ini
[General]
trusted_hosts[] = "app.tongji.edu.cn"
trusted_hosts[] = "**************:8080"
force_ssl = 1

# 如果支持，添加代理配置
proxy_client_headers[] = "HTTP_X_FORWARDED_FOR"
proxy_client_headers[] = "HTTP_X_REAL_IP"
proxy_host_headers[] = "HTTP_X_FORWARDED_HOST"
```

#### 方案 B：启用最小化的内容替换
如果 Matomo 仍然生成错误的路径，可以启用最小的 sub_filter：

```nginx
# 在 nginx 配置中取消注释并修改：
sub_filter_once off;
sub_filter_types text/html application/javascript;
sub_filter '/matomo/matomo/' '/matomo/';  # 只修复重复路径
```

## 调试信息

### 检查代理头是否正确传递
```bash
# 在 Matomo 服务器上临时添加日志
# 检查是否收到 X-Forwarded-Prefix 头
tail -f /var/log/apache2/access.log | grep "X-Forwarded-Prefix"
```

### 浏览器开发者工具检查
1. Network 面板查看请求头
2. 确认 `X-Forwarded-Prefix: /matomo` 是否正确发送
3. 查看响应中的 Location 头

## 预期结果

修复后应该：
- ✅ 访问 `https://app.tongji.edu.cn/matomo/` 正常
- ✅ 登录后跳转到 `https://app.tongji.edu.cn/matomo/index.php?module=CoreHome&action=index`
- ✅ 不再出现重复的 `/matomo/matomo/` 路径
- ✅ 所有功能正常工作

## 回滚方案

如果新配置有问题：
```bash
git revert HEAD
git push origin main
# 重新部署
```

---

**关键点**：这次修复的核心是让 Matomo 通过 `X-Forwarded-Prefix` 头自己正确处理路径，而不是通过 nginx 的内容替换来强制修改。这是更可靠的方法。
