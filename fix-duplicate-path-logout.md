# 修复 Matomo 重复路径和退出登录问题

## 问题分析

### 1. 重复路径问题 `/matomo/matomo/`
**原因**：`sub_filter` 规则与 Matomo 的 `X-Forwarded-Prefix` 处理冲突，导致路径被重复添加。

### 2. 退出登录需要两次
**原因**：Cookie 没有被正确清除，session 仍然有效。

## 修复方案

### 1. 完全禁用内容替换 ✅
- 移除所有 `sub_filter` 规则
- 让 Matomo 通过 `X-Forwarded-Prefix` 头自己处理所有路径
- 避免路径被重复修改

### 2. 强化 Cookie 处理 ✅
- 添加多重 cookie 域映射
- 对退出登录请求特殊处理
- 强制清除 session cookies

### 3. 退出登录特殊处理 ✅
- 检测退出登录请求（`action=logout`）
- 强制添加 cookie 清除头
- 禁用缓存确保立即生效

## 关键配置

```nginx
location /matomo/ {
    # 统一路径处理
    rewrite ^/matomo/(.*)$ /$1 break;
    proxy_pass http://192.168.177.64:8080;
    
    # 关键代理头
    proxy_set_header X-Forwarded-Prefix /matomo;
    proxy_set_header X-Forwarded-Uri /matomo;
    
    # 强化 cookie 处理
    proxy_cookie_path / /matomo/;
    proxy_cookie_domain ~^(.+)$ app.tongji.edu.cn;
    
    # 退出登录特殊处理
    if ($args ~ "action=logout") {
        add_header Set-Cookie "MATOMO_SESSID=; Path=/matomo/; Expires=Thu, 01 Jan 1970 00:00:00 GMT";
        add_header Set-Cookie "piwik_auth=; Path=/matomo/; Expires=Thu, 01 Jan 1970 00:00:00 GMT";
    }
    
    # 完全禁用内容替换
    # 让 Matomo 自己处理路径
}
```

## 测试步骤

### 1. 部署配置
```bash
git add nginx/default.conf
git commit -m "fix: 禁用内容替换修复重复路径，强化退出登录处理"
git push origin main
# GitLab 手动触发 deploy-nginx
```

### 2. 清除缓存
**重要**：清除浏览器所有缓存、cookies 和本地存储

### 3. 登录跳转测试
1. 访问：`https://app.tongji.edu.cn/matomo/`
2. 输入用户名密码登录
3. **检查登录后的 URL**：
   - ✅ 期望：`https://app.tongji.edu.cn/matomo/index.php?module=CoreHome&action=index`
   - ❌ 错误：`https://app.tongji.edu.cn/matomo/matomo/index.php`

### 4. 退出登录测试
1. 在已登录状态下
2. **点击退出登录一次**
3. **检查结果**：
   - ✅ 期望：直接跳转到登录页面
   - ❌ 错误：仍然显示仪表板或需要再次点击

### 5. Session 验证
退出后直接访问：`https://app.tongji.edu.cn/matomo/`
**期望结果**：显示登录页面，不是仪表板

## 为什么这次会成功

### 1. 根本性解决路径问题
- 完全移除了导致路径重复的 `sub_filter` 规则
- 依赖 Matomo 标准的代理头处理机制

### 2. 强化退出登录处理
- 检测退出请求并强制清除 cookies
- 多重 cookie 域映射确保覆盖所有情况
- 强制禁用缓存

### 3. 简化配置
- 减少了复杂的规则冲突
- 使用标准的代理模式

## 调试信息

### 如果登录后仍然跳转到重复路径
1. **检查 Matomo 配置**：确保删除了 `base_url` 和 `base_path`
2. **检查代理头**：在开发者工具中确认 `X-Forwarded-Prefix: /matomo`
3. **检查 nginx 日志**：
   ```bash
   docker logs app-nginx | grep "matomo" | tail -10
   ```

### 如果退出登录仍然需要两次
1. **检查 cookies**：在开发者工具 Application → Cookies 中查看
2. **检查退出请求**：在 Network 面板中查看退出请求的响应头
3. **手动清除 cookies**：删除所有 `app.tongji.edu.cn` 的 cookies

## 预期结果

修复后应该：
- ✅ 登录后跳转到正确的 URL（不含重复的 `/matomo/`）
- ✅ 退出登录一次点击即可成功
- ✅ 退出后无法直接访问仪表板
- ✅ 所有功能正常工作

---

**关键点**：这次修复的核心是完全依赖 Matomo 的标准代理处理机制，避免了 nginx 层面的路径干预导致的冲突。
