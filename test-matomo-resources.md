# Matomo 静态资源和退出登录测试

## 当前修复状态

### 1. 静态资源修复 ✅
- 修复了正则表达式以支持查询参数（如 `?matomo`）
- 新的匹配模式：`^/matomo/(.+\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))(\?.*)?$`

### 2. 退出登录修复 ✅
- 优化了 cookie 处理
- 添加了退出后重定向的路径修复

## 立即测试步骤

### 1. 部署配置
```bash
git add nginx/default.conf
git commit -m "fix: 修复静态资源查询参数匹配和退出登录重定向"
git push origin main
# 在 GitLab 中手动触发 deploy-nginx
```

### 2. 静态资源测试

#### 2.1 清除缓存
**重要**：清除浏览器缓存和 cookies

#### 2.2 测试具体资源
直接在浏览器中访问这些 URL：

1. **带查询参数的资源**：
   ```
   https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg?matomo
   ```

2. **其他常见静态资源**：
   ```
   https://app.tongji.edu.cn/matomo/plugins/Morpheus/stylesheets/base.css
   https://app.tongji.edu.cn/matomo/libs/bower_components/jquery/dist/jquery.min.js
   https://app.tongji.edu.cn/matomo/plugins/CoreHome/images/favicon.ico
   ```

#### 2.3 检查开发者工具
1. 打开浏览器开发者工具 → Network 面板
2. 访问 `https://app.tongji.edu.cn/matomo/`
3. 查看所有资源的加载状态
4. **重点检查**：是否还有 404 错误

### 3. 退出登录测试

#### 3.1 完整流程测试
1. 访问：`https://app.tongji.edu.cn/matomo/`
2. 登录管理员账号
3. 确认进入仪表板
4. **点击退出登录按钮**
5. **验证结果**：
   - ✅ 应该跳转到：`https://app.tongji.edu.cn/matomo/index.php?module=Login&action=login`
   - ❌ 不应该仍然显示仪表板

#### 3.2 Session 验证
退出后：
1. 直接访问 `https://app.tongji.edu.cn/matomo/`
2. 应该显示登录页面，而不是仪表板
3. 检查浏览器 cookies 是否被正确清除

## 调试方法

### 如果静态资源仍然 404

#### 方法 1：检查 nginx 日志
```bash
docker logs app-nginx | tail -50 | grep "404"
```

#### 方法 2：测试正则表达式匹配
在 nginx 配置中临时添加调试头：
```nginx
location ~* ^/matomo/(.+\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))(\?.*)?$ {
    add_header X-Debug-Match "YES";
    add_header X-Debug-File "$1";
    add_header X-Debug-Query "$3";
    # ... 其他配置
}
```

#### 方法 3：简化测试
临时创建一个更宽泛的匹配：
```nginx
location ~* ^/matomo/(.+)$ {
    proxy_pass http://**************:8080/$1;
    # ... 基本代理配置
}
```

### 如果退出登录仍然失败

#### 检查退出请求
1. 在开发者工具 Network 面板中
2. 点击退出登录
3. 查看发送的请求 URL 和响应
4. 检查响应头中的 `Location` 和 `Set-Cookie`

#### 可能的 Matomo 配置问题
在 Matomo 服务器的 `config/config.ini.php` 中添加：
```ini
[General]
trusted_hosts[] = "app.tongji.edu.cn"
force_ssl = 1

# 如果支持，添加退出后重定向
login_logout_url = "/matomo/index.php?module=Login&action=login"
```

## 预期结果

修复后应该：
- ✅ `https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg?matomo` 正常加载
- ✅ 所有静态资源无 404 错误
- ✅ 页面样式完整显示
- ✅ 退出登录后跳转到登录页面
- ✅ 退出后无法直接访问仪表板

## 如果问题持续

### 临时解决方案
如果静态资源问题持续，可以临时使用更简单的配置：

```nginx
# 替换复杂的静态资源匹配
location /matomo/ {
    proxy_pass http://**************:8080/;
    # ... 所有现有配置保持不变
    
    # 移除或注释掉单独的静态资源 location 块
}
```

这样所有请求都通过主要的 location 块处理，虽然缓存效果不如专门的静态资源处理，但可以确保功能正常。

---

**关键测试点**：
1. 带查询参数的静态资源是否正常加载
2. 退出登录是否正确跳转到登录页面
3. 退出后是否真正清除了 session
