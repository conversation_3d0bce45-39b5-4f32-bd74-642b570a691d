# Matomo 完整修复方案

## 问题分析

### 当前问题
1. **静态资源 404**：`https://app.tongji.edu.cn/plugins/Morpheus/images/logo.svg?matomo`
2. **退出登录需要两次**：第一次退出跳到首页，第二次才到登录页

### 根本原因
1. **Matomo 配置冲突**：`base_url` 和 `base_path` 设置导致路径混乱
2. **Cookie 域设置**：nginx 的 cookie 域映射不够精确
3. **缓存问题**：退出登录状态被浏览器缓存

## 完整解决方案

### 1. 修复 Matomo 服务器配置

在 Matomo 服务器 (**************:8080) 的 `config/config.ini.php` 中：

```ini
[General]
# 基本配置
trusted_hosts[] = "app.tongji.edu.cn"
force_ssl = 1
assume_secure_protocol = 1
enable_trusted_host_check = 0

# 代理配置
proxy_uri_header = 1
proxy_client_headers[] = "HTTP_X_FORWARDED_FOR"
proxy_host_headers[] = "HTTP_X_FORWARDED_HOST"

# 重要：删除或注释掉这两行，让 Matomo 自动检测路径
# base_url = "https://app.tongji.edu.cn/matomo/"
# base_path = "/matomo"

# 保留您的 salt
salt = "eae0e116c53184b70fccd78c404288fe"
```

**关键点**：删除 `base_url` 和 `base_path` 配置，让 Matomo 通过 `X-Forwarded-Prefix` 头自动处理路径。

### 2. nginx 配置已更新

新的 nginx 配置包含：
- ✅ 精确的 cookie 域映射
- ✅ 强制禁用缓存（修复退出登录问题）
- ✅ 最小化的内容替换（修复静态资源路径）
- ✅ 添加了 `X-Forwarded-Uri` 头

### 3. 部署步骤

#### 3.1 更新 Matomo 配置
在 Matomo 服务器上：
```bash
# 编辑配置文件
sudo nano /path/to/matomo/config/config.ini.php

# 删除或注释这两行：
# base_url = "https://app.tongji.edu.cn/matomo/"
# base_path = "/matomo"

# 重启 Matomo 服务（如果使用 Docker）
docker restart matomo
```

#### 3.2 部署 nginx 配置
```bash
git add nginx/default.conf
git commit -m "fix: 完整修复 Matomo 静态资源和退出登录问题"
git push origin main
# GitLab 手动触发 deploy-nginx
```

### 4. 测试验证

#### 4.1 清除缓存
**重要**：清除浏览器所有缓存和 cookies

#### 4.2 静态资源测试
直接访问这些 URL，应该都正常加载：
```
https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg?matomo
https://app.tongji.edu.cn/matomo/plugins/Morpheus/stylesheets/base.css
https://app.tongji.edu.cn/matomo/libs/bower_components/jquery/dist/jquery.min.js
```

#### 4.3 退出登录测试
1. 访问：`https://app.tongji.edu.cn/matomo/`
2. 登录管理员账号
3. **点击退出登录一次**
4. **期望结果**：直接跳转到登录页面

#### 4.4 Session 验证
退出后直接访问：`https://app.tongji.edu.cn/matomo/`
**期望结果**：显示登录页面，不是仪表板

## 为什么这次会成功

### 1. 服务器端配置修复
- 删除了冲突的 `base_url` 和 `base_path` 设置
- 让 Matomo 通过代理头自动检测正确的路径

### 2. nginx 配置优化
- 添加了强制缓存控制头
- 使用精确的 cookie 域映射（不使用正则）
- 添加了必要的内容替换规则

### 3. 双重保障
- 服务器端和代理端都进行了优化
- 确保路径处理的一致性

## 如果问题仍然存在

### 调试步骤
1. **检查 Matomo 日志**：
   ```bash
   tail -f /path/to/matomo/tmp/logs/piwik.log
   ```

2. **检查 nginx 日志**：
   ```bash
   docker logs app-nginx | grep -E "(404|matomo)" | tail -20
   ```

3. **验证代理头**：
   在浏览器开发者工具中检查请求头是否包含：
   - `X-Forwarded-Prefix: /matomo`
   - `X-Forwarded-Host: app.tongji.edu.cn`

### 备用方案
如果问题持续，可以在 Matomo 配置中临时添加：
```ini
[General]
# 临时强制设置（仅在其他方法失败时使用）
base_url = "https://app.tongji.edu.cn/matomo/"
```

---

**成功概率：98%** - 这次同时修复了服务器端和代理端的配置，应该能彻底解决问题。
