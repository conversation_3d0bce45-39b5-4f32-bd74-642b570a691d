# 快速修复验证 - Matomo 静态资源和退出登录

## 关键修复点

### 1. 静态资源修复 🔧
**问题**：`https://app.tongji.edu.cn/plugins/Morpheus/images/logo.svg?matomo` 404

**修复**：
- 使用 `rewrite ^/matomo/(.*)$ /$1 break;` 替代复杂的正则捕获
- 这样可以正确处理带查询参数的静态资源

### 2. 退出登录修复 🔧
**问题**：退出后仍然回到首页而不是登录页

**修复**：
- 优化了 cookie 处理：`proxy_cookie_flags ~ secure samesite=lax`
- 添加了退出重定向的路径修复

## 立即验证步骤

### 🚀 1. 部署
```bash
git add nginx/default.conf
git commit -m "fix: 使用 rewrite 修复静态资源查询参数问题"
git push origin main
# GitLab 手动触发 deploy-nginx
```

### 🧪 2. 静态资源测试
**清除浏览器缓存后**，直接访问：
```
https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg?matomo
```
**期望结果**：✅ 正常显示 SVG 图片，不是 404

### 🧪 3. 退出登录测试
1. 登录 Matomo：`https://app.tongji.edu.cn/matomo/`
2. 进入仪表板
3. **点击退出登录**
4. **期望结果**：✅ 跳转到 `https://app.tongji.edu.cn/matomo/index.php?module=Login&action=login`

### 🧪 4. Session 清除验证
退出后直接访问：`https://app.tongji.edu.cn/matomo/`
**期望结果**：✅ 显示登录页面，不是仪表板

## 如果仍有问题

### 静态资源仍然 404
**临时解决方案**：移除专门的静态资源 location，让所有请求通过主 location 处理

在 nginx 配置中注释掉：
```nginx
# location ~* ^/matomo/(.+\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))(\?.*)?$ {
#     ...
# }
```

### 退出登录仍然失败
**检查点**：
1. 浏览器开发者工具 → Network 面板
2. 点击退出登录，查看请求和响应
3. 检查响应头中的 `Location` 字段

**可能需要的 Matomo 配置**：
在 Matomo 服务器的 `config/config.ini.php` 中：
```ini
[General]
trusted_hosts[] = "app.tongji.edu.cn"
force_ssl = 1
```

## 调试命令

### 检查 nginx 日志
```bash
docker logs app-nginx | grep -E "(404|matomo)" | tail -20
```

### 测试静态资源代理
```bash
curl -I "https://app.tongji.edu.cn/matomo/plugins/Morpheus/images/logo.svg?matomo"
```

## 成功标志

修复成功后应该看到：
- ✅ 静态资源正常加载（无 404 错误）
- ✅ 页面样式完整显示
- ✅ 退出登录正确跳转
- ✅ 退出后无法直接访问仪表板

---

**重点**：这次修复的核心是使用 `rewrite` 指令来正确处理带查询参数的静态资源请求，这比复杂的正则捕获更可靠。
