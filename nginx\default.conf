map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

server {
    #添加 “nosniff”的 X-Content-Type-Options
    add_header X-Content-Type-Options nosniff;
    #隐藏版本号
    server_tokens off;
    #监听443端口
    listen 443 ssl;
    #你的域名
    server_name app.tongji.edu.cn;
    # ssl on;
    #ssl证书的pem文件路径
    ssl_certificate /etc/nginx/conf.d/ssl_crt.crt;
    #ssl证书的key文件路径
    ssl_certificate_key /etc/nginx/conf.d/ssl_key.key;

    # 文件大小限制在2g以内 大幅增加超时时间配置
    client_max_body_size 2g;  # 2GB，
    proxy_connect_timeout 1800s;  # 30分钟
    proxy_send_timeout 1800s;     # 30分钟  
    proxy_read_timeout 1800s;     # 30分钟
    client_body_timeout 1800s;    # 30分钟

    location /6QGhokRqT1.txt {
        root /etc/nginx/conf.d;
        try_files $uri =404;
    }


    location /wallbreakerApiTest/ {
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://***************:9000/;
        proxy_connect_timeout 100;
    }

    location /wallbreakerApi/ {
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://*************:9000/;
            proxy_connect_timeout 100;
    }

    
    location /wallbreakerAuth/ {
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://nic-sde.pages.tongji.edu.cn/wallbreaker/auth/;
            proxy_connect_timeout 100;
    }

       
    location /wallbreakerGame/ {
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://nic-sde.pages.tongji.edu.cn/wallbreaker/game/;
            proxy_connect_timeout 100;
    } 

    location /1h/ {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Remote_addr $remote_addr;

            proxy_pass http://localhost:3000/1h;
    }

      location /1h/_next/ {
        proxy_pass http://localhost:3000/1h/_next/;
        proxy_set_header Host $host;
        expires 365d;
        access_log off;
    }
    location ^~ /api/upload {
        proxy_pass http://localhost:3003/upload/api/upload;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 大文件上传专用配置
        client_max_body_size 2g;
        client_body_timeout 1800s;
        client_body_buffer_size 128k;
        client_body_temp_path /tmp/nginx_upload_temp;
        
        proxy_connect_timeout 1800s;
        proxy_send_timeout 1800s;
        proxy_read_timeout 1800s;
        proxy_buffering off;  # 关闭代理缓冲，减少内存使用
        proxy_request_buffering off;  # 关闭请求缓冲，支持流式上传
    }

    location /api {
            proxy_pass http://localhost:3000/api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /1hSuccess/ {
                proxy_pass http://localhost:3000/1hSuccess;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

    location /1hpage/ {
                proxy_pass http://localhost:3000/1hpage;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
     }

    location /calendar/ {
         proxy_set_header Host $host;
         proxy_set_header X-Real-IP $remote_addr;
         proxy_set_header X-Forwarded-For $remote_addr;
         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
         proxy_set_header Remote_addr $remote_addr;

         proxy_pass http://localhost:3001/pages;
    }

    location /calendar/_next/ {
         proxy_pass http://localhost:3001/calendar/_next/;
         proxy_set_header Host $host;
         expires 365d;
         access_log off;
    }
    location /upload {
        proxy_pass http://localhost:3003/upload;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        client_max_body_size 10m;
        # 处理_next静态资源
        location /upload/_next/static {
            proxy_pass http://localhost:3003/upload/_next/static;
            expires 365d;
            access_log off;
        }
    }
    location /upload/_next/ {
        proxy_pass http://localhost:3003/upload/_next/;
        proxy_set_header Host $host;
        expires 365d;
        access_log off;
    }
    location /upload/download/ {
        proxy_pass https://s3-jd.tongji.edu.cn/;
        proxy_set_header Host s3-jd.tongji.edu.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_redirect off;
        proxy_set_header User-Agent $http_user_agent;
    }
    location ^~ /f/ {
        proxy_pass https://s3-jd.tongji.edu.cn/app/;
        proxy_set_header Host s3-jd.tongji.edu.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_redirect off;
        proxy_set_header User-Agent $http_user_agent;
    }



    location /ppt_carousel {
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://localhost:3002/ppt_carousel;
        proxy_set_header Host $host;
        proxy_connect_timeout 100;
    }
    # Matomo 代理
    location /matomo/ {
        proxy_pass http://**************:8080/;
        proxy_set_header Accept-Encoding "";
        proxy_set_header Host app.tongji.edu.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host app.tongji.edu.cn;
        proxy_set_header X-Forwarded-Port 443;

        # 设置前缀头 - Matomo
        proxy_set_header X-Forwarded-Prefix /matomo;

        # 重定向规则 - 精确匹配
        proxy_redirect ~^https?://192\.168\.177\.64:8080/(.*)$ /matomo/$1;
        proxy_redirect ~^https?://app\.tongji\.edu\.cn/(.*)$ /matomo/$1;
        proxy_redirect / /matomo/;

        # cookie 路径和域 - 确保登录/退出正常工作
        proxy_cookie_path / /matomo/;
        proxy_cookie_domain ************** app.tongji.edu.cn;
        proxy_cookie_domain localhost app.tongji.edu.cn;

        # 确保 session cookie 正确处理
        proxy_cookie_flags ~ secure samesite=lax;

        # 允许跨域访问
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";

        # 启用静态资源路径
        sub_filter_once off;
        sub_filter_types text/html application/javascript application/json text/css;

        # 静态资源路径 - 只替换根路径开始的资源引用
        sub_filter 'src="/' 'src="/matomo/';
        sub_filter 'href="/' 'href="/matomo/';
        sub_filter 'url("/' 'url("/matomo/';
        sub_filter "url('/" "url('/matomo/";

        # AJAX 请求路径
        sub_filter '"url":"/' '"url":"/matomo/';
        sub_filter "'url':'/" "'url':'/matomo/";

        # 表单 action
        sub_filter 'action="/' 'action="/matomo/';

        # 退出登录重定向
        sub_filter 'module=Login' 'module=Login';
        sub_filter 'action=logout' 'action=logout';

        # 清理可能的重复路径
        sub_filter '/matomo/matomo/' '/matomo/';
        sub_filter '"/matomo/matomo/' '"/matomo/';
        sub_filter "'/matomo/matomo/" "'/matomo/";

        # Matomo 特定的配置
        sub_filter 'piwik_url = "/"' 'piwik_url = "/matomo/"';
        sub_filter 'var u="//"' 'var u="//app.tongji.edu.cn/matomo/"';

    }

    # Matomo 静态资源代理 - 处理 CSS, JS, 图片等
    location ~* ^/matomo/(.+\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))$ {
        proxy_pass http://**************:8080/$1;
        proxy_set_header Host app.tongji.edu.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 静态资源缓存
        expires 7d;
        add_header Cache-Control "public, immutable";

        # 允许跨域访问静态资源
        add_header Access-Control-Allow-Origin *;
    }



}

server {
    listen 80;
    rewrite ^(.*)$  https://$host$1 permanent;
}




